from datetime import datetime
from hotel import db

# Payment types
PAYMENT_TYPE_CASH = 'cash'
PAYMENT_TYPE_CARD = 'card'
PAYMENT_TYPE_BANK_TRANSFER = 'bank_transfer'

# Payment status
PAYMENT_STATUS_PENDING = 'pending'
PAYMENT_STATUS_COMPLETED = 'completed'
PAYMENT_STATUS_CANCELLED = 'cancelled'

class Payment(db.Model):
    __tablename__ = 'payments'
    
    id = db.Column(db.Integer, primary_key=True)
    booking_id = db.Column(db.Integer, db.ForeignKey('bookings.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    payment_type = db.Column(db.String(20), nullable=False, default=PAYMENT_TYPE_CASH)
    payment_date = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(20), default=PAYMENT_STATUS_COMPLETED)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __init__(self, booking_id, amount, payment_type=PAYMENT_TYPE_CASH, notes=None):
        self.booking_id = booking_id
        self.amount = amount
        self.payment_type = payment_type
        self.notes = notes
        self.status = PAYMENT_STATUS_COMPLETED
    
    def __repr__(self):
        return f'<Payment {self.id}: {self.amount} EGP>'
