{% extends 'base.html' %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h2 class="mb-4">
            <i class="fas fa-edit me-2"></i>تعديل الحجز #{{ booking.id }}
        </h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">الرئيسية</a></li>
                {% if current_user.is_admin() %}
                <li class="breadcrumb-item"><a href="{{ url_for('booking.index') }}">الحجوزات</a></li>
                {% else %}
                <li class="breadcrumb-item"><a href="{{ url_for('booking.my_bookings') }}">حجوزاتي</a></li>
                {% endif %}
                <li class="breadcrumb-item"><a href="{{ url_for('booking.details', id=booking.id) }}">تفاصيل الحجز #{{ booking.id }}</a></li>
                <li class="breadcrumb-item active">تعديل الحجز</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>تعديل بيانات الحجز
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.room_id.label(class="form-label") }}
                            {{ form.room_id(class="form-select") }}
                            {% if form.room_id.errors %}
                                <div class="text-danger">
                                    {% for error in form.room_id.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        {% if current_user.is_admin() %}
                        <div class="col-md-6 mb-3">
                            {{ form.customer_id.label(class="form-label") }}
                            {{ form.customer_id(class="form-select") }}
                            {% if form.customer_id.errors %}
                                <div class="text-danger">
                                    {% for error in form.customer_id.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        {% else %}
                        {{ form.customer_id(type="hidden") }}
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.check_in_date.label(class="form-label") }}
                            {{ form.check_in_date(class="form-control", type="date") }}
                            {% if form.check_in_date.errors %}
                                <div class="text-danger">
                                    {% for error in form.check_in_date.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.check_out_date.label(class="form-label") }}
                            {{ form.check_out_date(class="form-control", type="date") }}
                            {% if form.check_out_date.errors %}
                                <div class="text-danger">
                                    {% for error in form.check_out_date.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control", rows="3") }}
                        {% if form.notes.errors %}
                            <div class="text-danger">
                                {% for error in form.notes.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex gap-2">
                        {{ form.submit(class="btn btn-primary") }}
                        <a href="{{ url_for('booking.details', id=booking.id) }}" class="btn btn-secondary">إلغاء</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>معلومات الحجز الحالي
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>رقم الحجز:</strong> #{{ booking.id }}
                </div>
                <div class="mb-3">
                    <strong>الحالة:</strong>
                    {% if booking.status == 'pending' %}
                        <span class="badge bg-warning">في الانتظار</span>
                    {% elif booking.status == 'confirmed' %}
                        <span class="badge bg-success">مؤكد</span>
                    {% elif booking.status == 'checked_in' %}
                        <span class="badge bg-primary">تم تسجيل الدخول</span>
                    {% elif booking.status == 'checked_out' %}
                        <span class="badge bg-secondary">تم تسجيل الخروج</span>
                    {% elif booking.status == 'cancelled' %}
                        <span class="badge bg-danger">ملغي</span>
                    {% endif %}
                </div>
                <div class="mb-3">
                    <strong>الغرفة الحالية:</strong> {{ booking.room.room_number }}
                </div>
                <div class="mb-3">
                    <strong>العميل:</strong> {{ booking.customer.full_name }}
                </div>
                <div class="mb-3">
                    <strong>تاريخ الوصول الحالي:</strong> {{ booking.check_in_date.strftime('%Y-%m-%d') }}
                </div>
                <div class="mb-3">
                    <strong>تاريخ المغادرة الحالي:</strong> {{ booking.check_out_date.strftime('%Y-%m-%d') }}
                </div>
                <div class="mb-3">
                    <strong>المبلغ الإجمالي الحالي:</strong> {{ "%.2f"|format(booking.total_price) }} ريال
                </div>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> سيتم إعادة حساب المبلغ الإجمالي تلقائياً عند تغيير الغرفة أو التواريخ.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-calculate total price when room or dates change
        const roomSelect = document.getElementById('room_id');
        const checkInDate = document.getElementById('check_in_date');
        const checkOutDate = document.getElementById('check_out_date');
        
        function updatePricePreview() {
            // This is a simple preview - actual calculation happens on server
            const selectedRoom = roomSelect.options[roomSelect.selectedIndex];
            if (selectedRoom && checkInDate.value && checkOutDate.value) {
                const checkIn = new Date(checkInDate.value);
                const checkOut = new Date(checkOutDate.value);
                const days = Math.ceil((checkOut - checkIn) / (1000 * 60 * 60 * 24));
                
                if (days > 0) {
                    // Extract price from room option text (format: "Room - Type - Price")
                    const roomText = selectedRoom.text;
                    const priceMatch = roomText.match(/(\d+\.?\d*)/g);
                    if (priceMatch && priceMatch.length >= 3) {
                        const pricePerNight = parseFloat(priceMatch[2]);
                        const totalPrice = pricePerNight * days;
                        
                        // Show preview in console or add a preview element
                        console.log(`المبلغ المتوقع: ${totalPrice.toFixed(2)} ريال (${days} ليلة × ${pricePerNight} ريال)`);
                    }
                }
            }
        }
        
        if (roomSelect) roomSelect.addEventListener('change', updatePricePreview);
        if (checkInDate) checkInDate.addEventListener('change', updatePricePreview);
        if (checkOutDate) checkOutDate.addEventListener('change', updatePricePreview);
    });
</script>
{% endblock %}
