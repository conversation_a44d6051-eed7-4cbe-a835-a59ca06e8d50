{% set display_rooms = filtered_rooms if filtered_rooms is defined else rooms %}

{% if display_rooms|length > 0 %}
<div class="table-responsive">
    <table class="table table-striped table-hover">
        <thead>
            <tr>
                <th>رقم الغرفة</th>
                <th>نوع الغرفة</th>
                <th>السعة</th>
                <th>السعر لليلة</th>
                <th>الحالة</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            {% for room in display_rooms %}
            <tr>
                <td>{{ room.room_number }}</td>
                <td>
                    {% if room.room_type == 'single' %}
                        غرفة مفردة
                    {% elif room.room_type == 'double' %}
                        غرفة مزدوجة
                    {% elif room.room_type == 'suite' %}
                        جناح
                    {% elif room.room_type == 'deluxe' %}
                        غرفة فاخرة
                    {% endif %}
                </td>
                <td>{{ room.capacity }} شخص</td>
                <td>{{ room.price_per_night }} جنيه مصري</td>
                <td>
                    {% if room.status == 'available' %}
                        <span class="badge bg-success">متاحة</span>
                    {% elif room.status == 'occupied' %}
                        <span class="badge bg-danger">مشغولة</span>
                    {% elif room.status == 'maintenance' %}
                        <span class="badge bg-warning">صيانة</span>
                    {% endif %}
                </td>
                <td>
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('room.details', id=room.id) }}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </a>

                        {% if current_user.is_admin() %}
                        <a href="{{ url_for('room.edit', id=room.id) }}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </a>

                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteRoomModal{{ room.id }}" title="حذف">
                            <i class="fas fa-trash-alt"></i>
                        </button>

                        <!-- Delete Room Modal -->
                        <div class="modal fade" id="deleteRoomModal{{ room.id }}" tabindex="-1" aria-labelledby="deleteRoomModalLabel{{ room.id }}" aria-hidden="true">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="deleteRoomModalLabel{{ room.id }}">تأكيد حذف الغرفة</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        هل أنت متأكد من رغبتك في حذف الغرفة رقم {{ room.room_number }}؟
                                        {% if room.bookings.count() > 0 %}
                                        <div class="alert alert-danger mt-3">
                                            <i class="fas fa-exclamation-triangle me-2"></i>تحذير: هذه الغرفة مرتبطة بـ {{ room.bookings.count() }} حجز. لا يمكن حذفها.
                                        </div>
                                        {% endif %}
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                        <form action="{{ url_for('room.delete', id=room.id) }}" method="POST">
                                            <button type="submit" class="btn btn-danger" {% if room.bookings.count() > 0 %}disabled{% endif %}>تأكيد الحذف</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        {% if room.status == 'available' %}
                        <a href="{{ url_for('booking.create', room_id=room.id) }}" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="حجز الغرفة">
                            <i class="fas fa-calendar-plus"></i>
                        </a>
                        {% endif %}
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<div class="alert alert-info">
    <i class="fas fa-info-circle me-2"></i>لا توجد غرف في هذه القائمة
</div>
{% endif %}
