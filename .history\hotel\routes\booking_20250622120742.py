from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from sqlalchemy import and_, or_

from hotel import db
from hotel.models import Booking, Room, Customer
from hotel.forms.booking import BookingForm, BookingSearchForm
from hotel.utils.decorators import admin_required

booking_bp = Blueprint('booking', __name__, url_prefix='/bookings')

@booking_bp.route('/')
@login_required
@admin_required
def index():
    bookings = Booking.query.order_by(Booking.created_at.desc()).all()
    return render_template('booking/index.html', title='جميع الحجوزات', bookings=bookings)

@booking_bp.route('/my-bookings')
@login_required
def my_bookings():
    if current_user.is_admin():
        return redirect(url_for('booking.index'))

    bookings = Booking.query.filter_by(user_id=current_user.id).order_by(Booking.created_at.desc()).all()
    return render_template('booking/my_bookings.html', title='حجوزاتي', bookings=bookings)

@booking_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    form = BookingForm()

    # Populate room choices
    form.room_id.choices = [(room.id, f'{room.room_number} - {room.room_type} - {room.price_per_night}')
                           for room in Room.query.filter_by(status='available').all()]

    # Populate customer choices
    if current_user.is_admin():
        form.customer_id.choices = [(customer.id, f'{customer.full_name} - {customer.id_number}')
                                   for customer in Customer.query.all()]

    if form.validate_on_submit():
        # Check if room is available for the selected dates
        room_id = form.room_id.data
        check_in_date = form.check_in_date.data
        check_out_date = form.check_out_date.data

        # Check for overlapping bookings
        overlapping_bookings = Booking.query.filter(
            Booking.room_id == room_id,
            Booking.status.in_(['pending', 'confirmed', 'checked_in']),
            or_(
                and_(Booking.check_in_date <= check_in_date, Booking.check_out_date > check_in_date),
                and_(Booking.check_in_date < check_out_date, Booking.check_out_date >= check_out_date),
                and_(Booking.check_in_date >= check_in_date, Booking.check_out_date <= check_out_date)
            )
        ).all()

        if overlapping_bookings:
            flash('الغرفة غير متاحة في التواريخ المحددة', 'danger')
            return redirect(url_for('booking.create'))

        # Calculate total price
        room = Room.query.get(room_id)
        days = (check_out_date - check_in_date).days
        total_price = room.price_per_night * days

        # Create booking
        booking = Booking(
            user_id=current_user.id,
            room_id=room_id,
            customer_id=form.customer_id.data if current_user.is_admin() else form.customer_id.data,
            check_in_date=check_in_date,
            check_out_date=check_out_date,
            total_price=total_price,
            paid_amount=0.0,
            notes=form.notes.data
        )

        db.session.add(booking)
        db.session.commit()

        flash('تم إنشاء الحجز بنجاح', 'success')
        return redirect(url_for('booking.my_bookings' if not current_user.is_admin() else 'booking.index'))

    return render_template('booking/create.html', title='حجز جديد', form=form)

@booking_bp.route('/<int:id>')
@login_required
def details(id):
    booking = Booking.query.get_or_404(id)

    # Check if user has permission to view this booking
    if not current_user.is_admin() and booking.user_id != current_user.id:
        flash('ليس لديك صلاحية لعرض هذا الحجز', 'danger')
        return redirect(url_for('booking.my_bookings'))

    return render_template('booking/details.html', title=f'تفاصيل الحجز #{booking.id}', booking=booking)

@booking_bp.route('/<int:id>/cancel', methods=['POST'])
@login_required
def cancel(id):
    booking = Booking.query.get_or_404(id)

    # Check if user has permission to cancel this booking
    if not current_user.is_admin() and booking.user_id != current_user.id:
        flash('ليس لديك صلاحية لإلغاء هذا الحجز', 'danger')
        return redirect(url_for('booking.my_bookings'))

    # Check if booking can be cancelled
    if booking.status not in ['pending', 'confirmed']:
        flash('لا يمكن إلغاء هذا الحجز في حالته الحالية', 'danger')
        return redirect(url_for('booking.details', id=booking.id))

    booking.status = 'cancelled'
    db.session.commit()

    flash('تم إلغاء الحجز بنجاح', 'success')
    return redirect(url_for('booking.my_bookings' if not current_user.is_admin() else 'booking.index'))

@booking_bp.route('/<int:id>/confirm', methods=['POST'])
@login_required
@admin_required
def confirm(id):
    booking = Booking.query.get_or_404(id)

    # Check if booking can be confirmed
    if booking.status != 'pending':
        flash('لا يمكن تأكيد هذا الحجز في حالته الحالية', 'danger')
        return redirect(url_for('booking.details', id=booking.id))

    booking.status = 'confirmed'
    db.session.commit()

    flash('تم تأكيد الحجز بنجاح', 'success')
    return redirect(url_for('booking.details', id=booking.id))

@booking_bp.route('/<int:id>/check_in', methods=['POST'])
@login_required
@admin_required
def check_in(id):
    booking = Booking.query.get_or_404(id)

    # Check if booking can be checked in
    if booking.status != 'confirmed':
        flash('لا يمكن تسجيل الدخول لهذا الحجز في حالته الحالية', 'danger')
        return redirect(url_for('booking.details', id=booking.id))

    booking.status = 'checked_in'
    booking.room.status = 'occupied'
    db.session.commit()

    flash('تم تسجيل الدخول بنجاح', 'success')
    return redirect(url_for('booking.details', id=booking.id))

@booking_bp.route('/<int:id>/check_out', methods=['POST'])
@login_required
@admin_required
def check_out(id):
    booking = Booking.query.get_or_404(id)

    # Check if booking can be checked out
    if booking.status != 'checked_in':
        flash('لا يمكن تسجيل الخروج لهذا الحجز في حالته الحالية', 'danger')
        return redirect(url_for('booking.details', id=booking.id))

    booking.status = 'checked_out'
    booking.room.status = 'available'
    db.session.commit()

    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('booking.details', id=booking.id))
