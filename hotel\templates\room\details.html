{% extends 'base.html' %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h2 class="mb-4">
            <i class="fas fa-door-open me-2"></i>تفاصيل الغرفة {{ room.room_number }}
        </h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('room.index') }}">الغرف</a></li>
                <li class="breadcrumb-item active">تفاصيل الغرفة {{ room.room_number }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>معلومات الغرفة
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p class="mb-1 text-muted">رقم الغرفة</p>
                        <h5>{{ room.room_number }}</h5>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-1 text-muted">حالة الغرفة</p>
                        <h5>
                            {% if room.status == 'available' %}
                                <span class="badge bg-success">متاحة</span>
                            {% elif room.status == 'occupied' %}
                                <span class="badge bg-danger">مشغولة</span>
                            {% elif room.status == 'maintenance' %}
                                <span class="badge bg-warning">صيانة</span>
                            {% endif %}
                        </h5>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <p class="mb-1 text-muted">نوع الغرفة</p>
                        <h5>
                            {% if room.room_type == 'single' %}
                                غرفة مفردة
                            {% elif room.room_type == 'double' %}
                                غرفة مزدوجة
                            {% elif room.room_type == 'suite' %}
                                جناح
                            {% elif room.room_type == 'deluxe' %}
                                غرفة فاخرة
                            {% endif %}
                        </h5>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-1 text-muted">السعة</p>
                        <h5>{{ room.capacity }} شخص</h5>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <p class="mb-1 text-muted">السعر لليلة الواحدة</p>
                        <h5 class="text-success">{{ room.price_per_night }} جنيه مصري</h5>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-12">
                        <p class="mb-1 text-muted">وصف الغرفة</p>
                        <p>{{ room.description or 'لا يوجد وصف متاح' }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>الإجراءات
                </h5>
            </div>
            <div class="card-body">
                {% if room.status == 'available' %}
                <div class="d-grid gap-2">
                    <a href="{{ url_for('booking.create', room_id=room.id) }}" class="btn btn-primary">
                        <i class="fas fa-calendar-plus me-1"></i>حجز هذه الغرفة
                    </a>

                    {% if current_user.is_admin() %}
                    <a href="{{ url_for('room.edit', id=room.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i>تعديل بيانات الغرفة
                    </a>

                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteRoomModal">
                        <i class="fas fa-trash-alt me-1"></i>حذف الغرفة
                    </button>
                    {% endif %}
                </div>
                {% else %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>هذه الغرفة غير متاحة للحجز حالياً
                </div>

                {% if current_user.is_admin() %}
                <div class="d-grid gap-2 mt-3">
                    <a href="{{ url_for('room.edit', id=room.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i>تعديل بيانات الغرفة
                    </a>
                </div>
                {% endif %}
                {% endif %}

                <div class="d-grid mt-3">
                    <a href="{{ url_for('room.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>العودة إلى قائمة الغرف
                    </a>
                </div>
            </div>
        </div>

        {% if current_user.is_admin() %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>الحجوزات المرتبطة
                </h5>
            </div>
            <div class="card-body">
                {% if room.bookings.count() > 0 %}
                <ul class="list-group">
                    {% for booking in room.bookings %}
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <a href="{{ url_for('booking.details', id=booking.id) }}">حجز #{{ booking.id }}</a>
                            <div class="small text-muted">
                                {{ booking.check_in_date.strftime('%Y-%m-%d') }} إلى {{ booking.check_out_date.strftime('%Y-%m-%d') }}
                            </div>
                        </div>
                        <span class="badge bg-primary rounded-pill">{{ (booking.check_out_date - booking.check_in_date).days }} ليلة</span>
                    </li>
                    {% endfor %}
                </ul>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>لا توجد حجوزات مرتبطة بهذه الغرفة
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Room Modal -->
{% if current_user.is_admin() %}
<div class="modal fade" id="deleteRoomModal" tabindex="-1" aria-labelledby="deleteRoomModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteRoomModalLabel">تأكيد حذف الغرفة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من رغبتك في حذف الغرفة رقم {{ room.room_number }}؟
                {% if room.bookings.count() > 0 %}
                <div class="alert alert-danger mt-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>تحذير: هذه الغرفة مرتبطة بـ {{ room.bookings.count() }} حجز. لا يمكن حذفها.
                </div>
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form action="{{ url_for('room.delete', id=room.id) }}" method="POST">
                    <button type="submit" class="btn btn-danger" {% if room.bookings.count() > 0 %}disabled{% endif %}>تأكيد الحذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
