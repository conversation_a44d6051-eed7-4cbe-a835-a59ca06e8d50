# QARUN HOTEL

نظام متكامل لإدارة الفنادق مبني باستخدام Python وإطار العمل Flask.

## الميزات

- **نظام تسجيل الدخول للمستخدمين**:
  - نوعان من المستخدمين: موظف الاستقبال (Admin) والنزلاء (Guests)
  - دعم لتسجيل وتوثيق الدخول، وتغيير كلمة المرور

- **إدارة الغرف**:
  - عرض قائمة الغرف المتاحة والمشغولة
  - إضافة، تعديل، حذف الغرف
  - تحديد مواصفات كل غرفة (رقم، نوع، سعر، حالة)

- **الحجوزات**:
  - إمكانية حجز غرفة لتاريخ معين
  - التحقق من توفر الغرف في الفترة المطلوبة
  - عرض الحجز حسب المستخدم أو حسب التواريخ

- **إدارة العملاء**:
  - إضافة بيانات العميل (اسم، رقم الهوية، هاتف، بريد)
  - ربط كل عميل بالحجز الذي قام به

- **لوحة تحكم للمسؤول**:
  - رؤية إجمالية للحجوزات، الغرف، والإيرادات

## متطلبات التشغيل

- Python 3.8+
- Flask
- Flask-SQLAlchemy
- Flask-Login
- Flask-WTF
- Flask-Migrate
- وغيرها من المكتبات (مذكورة في ملف requirements.txt)

## طريقة التثبيت

1. قم بإنشاء بيئة افتراضية:
   ```
   python -m venv venv
   ```

2. قم بتفعيل البيئة الافتراضية:
   - في Windows:
     ```
     venv\Scripts\activate
     ```
   - في Linux/Mac:
     ```
     source venv/bin/activate
     ```

3. قم بتثبيت المكتبات المطلوبة:
   ```
   pip install -r requirements.txt
   ```

4. قم بتهيئة قاعدة البيانات وإضافة بيانات تجريبية:
   ```
   python init_db.py
   ```

5. قم بتشغيل التطبيق:
   ```
   python run.py
   ```

6. افتح المتصفح على العنوان:
   ```
   http://localhost:5000
   ```

## بيانات الدخول الافتراضية

- **حساب المسؤول**:
  - اسم المستخدم: admin
  - كلمة المرور: admin123

- **حساب النزيل**:
  - اسم المستخدم: guest
  - كلمة المرور: guest123

## هيكل المشروع

```
hotel/
├── __init__.py         # تهيئة التطبيق
├── models/             # نماذج قاعدة البيانات
├── routes/             # مسارات التطبيق
├── forms/              # نماذج الإدخال
├── templates/          # قوالب HTML
├── static/             # الملفات الثابتة (CSS, JS, الصور)
└── utils/              # أدوات مساعدة
```

## المساهمة

نرحب بمساهماتكم لتحسين هذا المشروع. يرجى إنشاء fork للمشروع وإرسال pull request بالتعديلات المقترحة.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
