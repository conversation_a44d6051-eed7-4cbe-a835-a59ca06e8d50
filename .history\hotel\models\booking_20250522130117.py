from datetime import datetime
from hotel import db

# Booking status
BOOKING_STATUS_PENDING = 'pending'
BOOKING_STATUS_CONFIRMED = 'confirmed'
BOOKING_STATUS_CHECKED_IN = 'checked_in'
BOOKING_STATUS_CHECKED_OUT = 'checked_out'
BOOKING_STATUS_CANCELLED = 'cancelled'

class Booking(db.Model):
    __tablename__ = 'bookings'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    room_id = db.Column(db.Integer, db.<PERSON>Key('rooms.id'), nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False)
    check_in_date = db.Column(db.Date, nullable=False)
    check_out_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default=BOOKING_STATUS_PENDING)
    total_price = db.Column(db.Float)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    notes = db.Column(db.Text)
    
    def __init__(self, user_id, room_id, customer_id, check_in_date, check_out_date, total_price=None, notes=None):
        self.user_id = user_id
        self.room_id = room_id
        self.customer_id = customer_id
        self.check_in_date = check_in_date
        self.check_out_date = check_out_date
        self.total_price = total_price
        self.notes = notes
        self.status = BOOKING_STATUS_PENDING
    
    def is_active(self):
        return self.status in [BOOKING_STATUS_PENDING, BOOKING_STATUS_CONFIRMED, BOOKING_STATUS_CHECKED_IN]
    
    def __repr__(self):
        return f'<Booking {self.id}>'
