{% set display_bookings = filtered_bookings if filtered_bookings is defined else bookings %}

{% if display_bookings|length > 0 %}
<div class="table-responsive">
    <table class="table table-striped table-hover">
        <thead>
            <tr>
                <th>رقم الحجز</th>
                <th>العميل</th>
                <th>الغرفة</th>
                <th>تاريخ الوصول</th>
                <th>تاريخ المغادرة</th>
                <th>المدة</th>
                <th>الحالة</th>
                <th>السعر الإجمالي</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            {% for booking in display_bookings %}
            <tr>
                <td>{{ booking.id }}</td>
                <td>{{ booking.customer.full_name }}</td>
                <td>{{ booking.room.room_number }}</td>
                <td>{{ booking.check_in_date.strftime('%Y-%m-%d') }}</td>
                <td>{{ booking.check_out_date.strftime('%Y-%m-%d') }}</td>
                <td>{{ (booking.check_out_date - booking.check_in_date).days }} ليلة</td>
                <td>
                    {% if booking.status == 'pending' %}
                        <span class="badge bg-warning">قيد الانتظار</span>
                    {% elif booking.status == 'confirmed' %}
                        <span class="badge bg-primary">مؤكد</span>
                    {% elif booking.status == 'checked_in' %}
                        <span class="badge bg-success">تم تسجيل الدخول</span>
                    {% elif booking.status == 'checked_out' %}
                        <span class="badge bg-secondary">تم تسجيل الخروج</span>
                    {% elif booking.status == 'cancelled' %}
                        <span class="badge bg-danger">ملغي</span>
                    {% endif %}
                </td>
                <td>{{ booking.total_price }} دينار</td>
                <td>
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('booking.details', id=booking.id) }}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </a>

                        {% if booking.status in ['pending', 'confirmed'] %}
                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#cancelBookingModal{{ booking.id }}" title="إلغاء الحجز">
                            <i class="fas fa-times"></i>
                        </button>

                        <!-- Cancel Booking Modal -->
                        <div class="modal fade" id="cancelBookingModal{{ booking.id }}" tabindex="-1" aria-labelledby="cancelBookingModalLabel{{ booking.id }}" aria-hidden="true">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="cancelBookingModalLabel{{ booking.id }}">تأكيد إلغاء الحجز</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        هل أنت متأكد من رغبتك في إلغاء الحجز رقم {{ booking.id }}؟
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                        <form action="{{ url_for('booking.cancel', id=booking.id) }}" method="POST">
                                            <button type="submit" class="btn btn-danger">تأكيد الإلغاء</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        {% if current_user.is_admin() %}
                            {% if booking.status == 'pending' %}
                            <form action="{{ url_for('booking.confirm', id=booking.id) }}" method="POST">
                                <button type="submit" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="تأكيد الحجز">
                                    <i class="fas fa-check"></i>
                                </button>
                            </form>
                            {% elif booking.status == 'confirmed' %}
                            <form action="{{ url_for('booking.check_in', id=booking.id) }}" method="POST">
                                <button type="submit" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="تسجيل الدخول">
                                    <i class="fas fa-sign-in-alt"></i>
                                </button>
                            </form>
                            {% elif booking.status == 'checked_in' %}
                            <form action="{{ url_for('booking.check_out', id=booking.id) }}" method="POST">
                                <button type="submit" class="btn btn-sm btn-secondary" data-bs-toggle="tooltip" title="تسجيل الخروج">
                                    <i class="fas fa-sign-out-alt"></i>
                                </button>
                            </form>
                            {% endif %}
                        {% endif %}
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<div class="alert alert-info">
    <i class="fas fa-info-circle me-2"></i>لا توجد حجوزات في هذه القائمة
</div>
{% endif %}
