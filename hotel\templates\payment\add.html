{% extends 'base.html' %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>إضافة دفعة - حجز رقم {{ booking.id }}
                </h5>
            </div>
            <div class="card-body">
                <!-- معلومات الحجز -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">معلومات الحجز</h6>
                                <p class="mb-1"><strong>العميل:</strong> {{ booking.customer.full_name }}</p>
                                <p class="mb-1"><strong>الغرفة:</strong> {{ booking.room.room_number }}</p>
                                <p class="mb-1"><strong>المدة:</strong> {{ (booking.check_out_date - booking.check_in_date).days }} ليلة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">معلومات الدفع</h6>
                                <p class="mb-1"><strong>المبلغ الإجمالي:</strong> <span class="text-primary">{{ booking.total_price }} جنيه مصري</span></p>
                                <p class="mb-1"><strong>المبلغ المدفوع:</strong> <span class="text-success">{{ booking.paid_amount or 0 }} جنيه مصري</span></p>
                                <p class="mb-1"><strong>المبلغ المتبقي:</strong> <span class="text-danger">{{ booking.remaining_amount }} جنيه مصري</span></p>
                                <div class="progress mt-2">
                                    <div class="progress-bar bg-success" role="progressbar" 
                                         style="width: {{ booking.payment_percentage }}%" 
                                         aria-valuenow="{{ booking.payment_percentage }}" 
                                         aria-valuemin="0" aria-valuemax="100">
                                        {{ "%.1f"|format(booking.payment_percentage) }}%
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- نموذج إضافة الدفعة -->
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.amount.label(class="form-label") }}
                                {{ form.amount(class="form-control", max=booking.remaining_amount) }}
                                {% if form.amount.errors %}
                                    <div class="text-danger">
                                        {% for error in form.amount.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">الحد الأقصى: {{ booking.remaining_amount }} جنيه مصري</small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.payment_type.label(class="form-label") }}
                                {{ form.payment_type(class="form-select") }}
                                {% if form.payment_type.errors %}
                                    <div class="text-danger">
                                        {% for error in form.payment_type.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control", rows="3") }}
                        {% if form.notes.errors %}
                            <div class="text-danger">
                                {% for error in form.notes.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('booking.details', id=booking.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>رجوع
                        </a>
                        {{ form.submit(class="btn btn-success") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
