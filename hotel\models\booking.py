from datetime import datetime
from hotel import db

# Booking status
BOOKING_STATUS_PENDING = 'pending'
BOOKING_STATUS_CONFIRMED = 'confirmed'
BOOKING_STATUS_CHECKED_IN = 'checked_in'
BOOKING_STATUS_CHECKED_OUT = 'checked_out'
BOOKING_STATUS_CANCELLED = 'cancelled'

class Booking(db.Model):
    __tablename__ = 'bookings'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    room_id = db.Column(db.Integer, db.<PERSON>Key('rooms.id'), nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False)
    check_in_date = db.Column(db.Date, nullable=False)
    check_out_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default=BOOKING_STATUS_PENDING)
    total_price = db.Column(db.Float)
    paid_amount = db.Column(db.Float, default=0.0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    notes = db.Column(db.Text)

    # Relationships
    payments = db.relationship('Payment', backref='booking', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, user_id, room_id, customer_id, check_in_date, check_out_date, total_price=None, paid_amount=0.0, notes=None):
        self.user_id = user_id
        self.room_id = room_id
        self.customer_id = customer_id
        self.check_in_date = check_in_date
        self.check_out_date = check_out_date
        self.total_price = total_price
        self.paid_amount = paid_amount
        self.notes = notes
        self.status = BOOKING_STATUS_PENDING
    
    def is_active(self):
        return self.status in [BOOKING_STATUS_PENDING, BOOKING_STATUS_CONFIRMED, BOOKING_STATUS_CHECKED_IN]

    @property
    def remaining_amount(self):
        """حساب المبلغ المتبقي"""
        return (self.total_price or 0) - (self.paid_amount or 0)

    @property
    def is_fully_paid(self):
        """التحقق من اكتمال الدفع"""
        return self.remaining_amount <= 0

    @property
    def payment_percentage(self):
        """حساب نسبة الدفع"""
        if not self.total_price or self.total_price == 0:
            return 0
        return min(100, (self.paid_amount or 0) / self.total_price * 100)

    def update_paid_amount(self):
        """تحديث المبلغ المدفوع من مجموع الدفعات"""
        total_paid = sum(payment.amount for payment in self.payments if payment.status == 'completed')
        self.paid_amount = total_paid

    def __repr__(self):
        return f'<Booking {self.id}>'
