<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
        <a class="navbar-brand" href="{{ url_for('main.index') }}">
            <i class="fas fa-hotel me-2"></i>نظام إدارة الفندق
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                {% if current_user.is_authenticated %}
                    {% if current_user.is_admin() %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'admin.dashboard' %}active{% endif %}" href="{{ url_for('admin.dashboard') }}">
                                <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint.startswith('room.') %}active{% endif %}" href="{{ url_for('room.index') }}">
                                <i class="fas fa-door-open me-1"></i>الغرف
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint.startswith('booking.') and request.endpoint != 'booking.my_bookings' %}active{% endif %}" href="{{ url_for('booking.index') }}">
                                <i class="fas fa-calendar-check me-1"></i>الحجوزات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint.startswith('customer.') %}active{% endif %}" href="{{ url_for('customer.index') }}">
                                <i class="fas fa-users me-1"></i>العملاء
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'admin.users' %}active{% endif %}" href="{{ url_for('admin.users') }}">
                                <i class="fas fa-user-shield me-1"></i>المستخدمين
                            </a>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'room.available' %}active{% endif %}" href="{{ url_for('room.available') }}">
                                <i class="fas fa-door-open me-1"></i>الغرف المتاحة
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'booking.my_bookings' %}active{% endif %}" href="{{ url_for('booking.my_bookings') }}">
                                <i class="fas fa-calendar-check me-1"></i>حجوزاتي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'booking.create' %}active{% endif %}" href="{{ url_for('booking.create') }}">
                                <i class="fas fa-plus-circle me-1"></i>حجز جديد
                            </a>
                        </li>
                    {% endif %}
                {% endif %}
            </ul>
            
            <ul class="navbar-nav">
                {% if current_user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i>{{ current_user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                                    <i class="fas fa-key me-1"></i>تغيير كلمة المرور
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                    <i class="fas fa-sign-out-alt me-1"></i>تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </li>
                {% else %}
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'auth.login' %}active{% endif %}" href="{{ url_for('auth.login') }}">
                            <i class="fas fa-sign-in-alt me-1"></i>تسجيل الدخول
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'auth.register' %}active{% endif %}" href="{{ url_for('auth.register') }}">
                            <i class="fas fa-user-plus me-1"></i>تسجيل حساب جديد
                        </a>
                    </li>
                {% endif %}
            </ul>
        </div>
    </div>
</nav>
