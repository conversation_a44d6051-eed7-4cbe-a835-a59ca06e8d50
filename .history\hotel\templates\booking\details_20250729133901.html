{% extends 'base.html' %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h2 class="mb-4">
            <i class="fas fa-calendar-check me-2"></i>تفاصيل الحجز #{{ booking.id }}
        </h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">الرئيسية</a></li>
                {% if current_user.is_admin() %}
                <li class="breadcrumb-item"><a href="{{ url_for('booking.index') }}">الحجوزات</a></li>
                {% else %}
                <li class="breadcrumb-item"><a href="{{ url_for('booking.my_bookings') }}">حجوزاتي</a></li>
                {% endif %}
                <li class="breadcrumb-item active">تفاصيل الحجز #{{ booking.id }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>معلومات الحجز
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p class="mb-1 text-muted">رقم الحجز</p>
                        <h5>{{ booking.id }}</h5>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-1 text-muted">حالة الحجز</p>
                        <h5>
                            {% if booking.status == 'pending' %}
                                <span class="badge bg-warning">قيد الانتظار</span>
                            {% elif booking.status == 'confirmed' %}
                                <span class="badge bg-primary">مؤكد</span>
                            {% elif booking.status == 'checked_in' %}
                                <span class="badge bg-success">تم تسجيل الدخول</span>
                            {% elif booking.status == 'checked_out' %}
                                <span class="badge bg-secondary">تم تسجيل الخروج</span>
                            {% elif booking.status == 'cancelled' %}
                                <span class="badge bg-danger">ملغي</span>
                            {% endif %}
                        </h5>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <p class="mb-1 text-muted">تاريخ الوصول</p>
                        <h5>{{ booking.check_in_date.strftime('%Y-%m-%d') }}</h5>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-1 text-muted">تاريخ المغادرة</p>
                        <h5>{{ booking.check_out_date.strftime('%Y-%m-%d') }}</h5>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <p class="mb-1 text-muted">عدد الليالي</p>
                        <h5>{{ (booking.check_out_date - booking.check_in_date).days }} ليلة</h5>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-1 text-muted">السعر الإجمالي</p>
                        <h5 class="text-primary">{{ booking.total_price }} جنيه مصري</h5>
                    </div>
                </div>

                <!-- معلومات الدفع -->
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title mb-3">
                                    <i class="fas fa-money-bill-wave me-2"></i>معلومات الدفع
                                </h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <p class="mb-1 text-muted">المبلغ المدفوع</p>
                                        <h6 class="text-success">{{ booking.paid_amount or 0 }} جنيه مصري</h6>
                                    </div>
                                    <div class="col-md-4">
                                        <p class="mb-1 text-muted">المبلغ المتبقي</p>
                                        <h6 class="text-danger">{{ booking.remaining_amount }} جنيه مصري</h6>
                                    </div>
                                    <div class="col-md-4">
                                        <p class="mb-1 text-muted">نسبة الدفع</p>
                                        <div class="progress">
                                            <div class="progress-bar bg-success" role="progressbar"
                                                 style="width: {{ booking.payment_percentage }}%"
                                                 aria-valuenow="{{ booking.payment_percentage }}"
                                                 aria-valuemin="0" aria-valuemax="100">
                                                {{ "%.1f"|format(booking.payment_percentage) }}%
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% if current_user.is_admin() and booking.remaining_amount > 0 %}
                                <div class="mt-3">
                                    <a href="{{ url_for('payment.add_payment', booking_id=booking.id) }}" class="btn btn-sm btn-success">
                                        <i class="fas fa-plus-circle me-1"></i>إضافة دفعة
                                    </a>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-12">
                        <p class="mb-1 text-muted">ملاحظات</p>
                        <p>{{ booking.notes or 'لا توجد ملاحظات' }}</p>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <p class="mb-1 text-muted">تاريخ الحجز</p>
                        <h5>{{ booking.created_at.strftime('%Y-%m-%d %H:%M') }}</h5>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-1 text-muted">تم الحجز بواسطة</p>
                        <h5>{{ booking.user.username }}</h5>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-door-open me-2"></i>معلومات الغرفة
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-1 text-muted">رقم الغرفة</p>
                <h5 class="mb-3">{{ booking.room.room_number }}</h5>

                <p class="mb-1 text-muted">نوع الغرفة</p>
                <h5 class="mb-3">
                    {% if booking.room.room_type == 'single' %}
                        غرفة مفردة
                    {% elif booking.room.room_type == 'double' %}
                        غرفة مزدوجة
                    {% elif booking.room.room_type == 'suite' %}
                        جناح
                    {% elif booking.room.room_type == 'deluxe' %}
                        غرفة فاخرة
                    {% endif %}
                </h5>

                <p class="mb-1 text-muted">السعر لليلة الواحدة</p>
                <h5 class="mb-3">{{ booking.room.price_per_night }} جنيه مصري</h5>

                <p class="mb-1 text-muted">السعة</p>
                <h5 class="mb-3">{{ booking.room.capacity }} شخص</h5>

                <div class="d-grid">
                    <a href="{{ url_for('room.details', id=booking.room.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-info-circle me-1"></i>تفاصيل الغرفة
                    </a>
                </div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>معلومات العميل
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-1 text-muted">الاسم</p>
                <h5 class="mb-3">{{ booking.customer.full_name }}</h5>

                <p class="mb-1 text-muted">رقم الهوية</p>
                <h5 class="mb-3">{{ booking.customer.id_number }}</h5>

                {% if booking.customer.phone %}
                <p class="mb-1 text-muted">رقم الهاتف</p>
                <h5 class="mb-3">{{ booking.customer.phone }}</h5>
                {% endif %}

                {% if booking.customer.email %}
                <p class="mb-1 text-muted">البريد الإلكتروني</p>
                <h5 class="mb-3">{{ booking.customer.email }}</h5>
                {% endif %}

                {% if current_user.is_admin() %}
                <div class="d-grid">
                    <a href="{{ url_for('customer.details', id=booking.customer.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-info-circle me-1"></i>تفاصيل العميل
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- تاريخ الدفعات -->
{% if booking.payments.count() > 0 %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-receipt me-2"></i>تاريخ الدفعات
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>الحالة</th>
                                <th>ملاحظات</th>
                                {% if current_user.is_admin() %}
                                <th>الإجراءات</th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in booking.payments %}
                            <tr>
                                <td>{{ payment.payment_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td class="text-success">{{ payment.amount }} جنيه مصري</td>
                                <td>
                                    {% if payment.payment_type == 'cash' %}
                                        <span class="badge bg-success">نقدي</span>
                                    {% elif payment.payment_type == 'card' %}
                                        <span class="badge bg-primary">بطاقة ائتمان</span>
                                    {% elif payment.payment_type == 'bank_transfer' %}
                                        <span class="badge bg-info">تحويل بنكي</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if payment.status == 'completed' %}
                                        <span class="badge bg-success">مكتمل</span>
                                    {% elif payment.status == 'pending' %}
                                        <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif payment.status == 'cancelled' %}
                                        <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                </td>
                                <td>{{ payment.notes or '-' }}</td>
                                {% if current_user.is_admin() %}
                                <td>
                                    <form action="{{ url_for('payment.delete_payment', payment_id=payment.id) }}" method="POST" style="display: inline;">
                                        <button type="submit" class="btn btn-sm btn-danger"
                                                onclick="return confirm('هل أنت متأكد من حذف هذه الدفعة؟')"
                                                data-bs-toggle="tooltip" title="حذف الدفعة">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </form>
                                </td>
                                {% endif %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>الإجراءات
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex gap-2">
                    {% if booking.status in ['pending', 'confirmed'] %}
                    <a href="{{ url_for('booking.edit', id=booking.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i>تعديل الحجز
                    </a>

                    <form action="{{ url_for('booking.cancel', id=booking.id) }}" method="POST">
                        <button type="submit" class="btn btn-danger btn-cancel-booking">
                            <i class="fas fa-times-circle me-1"></i>إلغاء الحجز
                        </button>
                    </form>
                    {% endif %}

                    {% if current_user.is_admin() and booking.status == 'pending' %}
                    <form action="{{ url_for('booking.confirm', id=booking.id) }}" method="POST">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-check-circle me-1"></i>تأكيد الحجز
                        </button>
                    </form>
                    {% endif %}

                    {% if current_user.is_admin() and booking.status == 'confirmed' %}
                    <form action="{{ url_for('booking.check_in', id=booking.id) }}" method="POST">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-1"></i>تسجيل الدخول
                        </button>
                    </form>
                    {% endif %}

                    {% if current_user.is_admin() and booking.status == 'checked_in' %}
                    <form action="{{ url_for('booking.check_out', id=booking.id) }}" method="POST">
                        <button type="submit" class="btn btn-secondary">
                            <i class="fas fa-sign-out-alt me-1"></i>تسجيل الخروج
                        </button>
                    </form>
                    {% endif %}

                    {% if current_user.is_admin() %}
                    <a href="{{ url_for('booking.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>العودة إلى قائمة الحجوزات
                    </a>
                    {% else %}
                    <a href="{{ url_for('booking.my_bookings') }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>العودة إلى حجوزاتي
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
