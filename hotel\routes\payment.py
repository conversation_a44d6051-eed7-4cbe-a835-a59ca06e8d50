from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user

from hotel import db
from hotel.models import Payment, Booking
from hotel.forms.payment import PaymentForm
from hotel.utils.decorators import admin_required

payment_bp = Blueprint('payment', __name__, url_prefix='/payments')

@payment_bp.route('/add/<int:booking_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def add_payment(booking_id):
    booking = Booking.query.get_or_404(booking_id)
    form = PaymentForm(booking=booking)
    form.booking_id.data = booking_id
    
    if form.validate_on_submit():
        # إنشاء دفعة جديدة
        payment = Payment(
            booking_id=booking_id,
            amount=form.amount.data,
            payment_type=form.payment_type.data,
            notes=form.notes.data
        )
        
        db.session.add(payment)
        db.session.flush()  # للحصول على ID الدفعة

        # تحديث المبلغ المدفوع في الحجز
        booking.update_paid_amount()

        db.session.commit()
        
        flash(f'تم تسجيل دفعة بمبلغ {form.amount.data} جنيه مصري بنجاح', 'success')
        return redirect(url_for('booking.details', id=booking_id))
    
    return render_template('payment/add.html', 
                         title='إضافة دفعة', 
                         form=form, 
                         booking=booking)

@payment_bp.route('/delete/<int:payment_id>', methods=['POST'])
@login_required
@admin_required
def delete_payment(payment_id):
    payment = Payment.query.get_or_404(payment_id)
    booking = payment.booking
    
    db.session.delete(payment)
    
    # تحديث المبلغ المدفوع في الحجز
    booking.update_paid_amount()
    
    db.session.commit()
    
    flash('تم حذف الدفعة بنجاح', 'success')
    return redirect(url_for('booking.details', id=booking.id))
