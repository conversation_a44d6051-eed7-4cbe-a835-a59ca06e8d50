{% extends 'base.html' %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h2 class="mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
        </h2>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="card bg-primary text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">{{ total_rooms }}</h5>
                        <div class="small">إجمالي الغرف</div>
                    </div>
                    <div>
                        <i class="fas fa-door-open fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="{{ url_for('room.index') }}">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-success text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">{{ available_rooms }}</h5>
                        <div class="small">الغرف المتاحة</div>
                    </div>
                    <div>
                        <i class="fas fa-check-circle fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="{{ url_for('room.available') }}">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-warning text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">{{ active_bookings }}</h5>
                        <div class="small">الحجوزات النشطة</div>
                    </div>
                    <div>
                        <i class="fas fa-calendar-check fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="{{ url_for('booking.index') }}">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-info text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">{{ total_customers }}</h5>
                        <div class="small">إجمالي العملاء</div>
                    </div>
                    <div>
                        <i class="fas fa-users fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="{{ url_for('customer.index') }}">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
</div>

<!-- Revenue Card -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-line me-1"></i>
                الإيرادات الشهرية
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="text-success">{{ monthly_revenue }} جنيه مصري</h3>
                    <div>
                        <a href="{{ url_for('booking.index') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-search me-1"></i>عرض التفاصيل
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Bookings -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-calendar-alt me-1"></i>
                أحدث الحجوزات
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>رقم الحجز</th>
                                <th>العميل</th>
                                <th>الغرفة</th>
                                <th>تاريخ الوصول</th>
                                <th>تاريخ المغادرة</th>
                                <th>الحالة</th>
                                <th>السعر الإجمالي</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for booking in recent_bookings %}
                            <tr>
                                <td>{{ booking.id }}</td>
                                <td>{{ booking.customer.full_name }}</td>
                                <td>{{ booking.room.room_number }}</td>
                                <td>{{ booking.check_in_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ booking.check_out_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if booking.status == 'pending' %}
                                        <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif booking.status == 'confirmed' %}
                                        <span class="badge bg-primary">مؤكد</span>
                                    {% elif booking.status == 'checked_in' %}
                                        <span class="badge bg-success">تم تسجيل الدخول</span>
                                    {% elif booking.status == 'checked_out' %}
                                        <span class="badge bg-secondary">تم تسجيل الخروج</span>
                                    {% elif booking.status == 'cancelled' %}
                                        <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                </td>
                                <td>{{ booking.total_price }} جنيه مصري</td>
                                <td>
                                    <a href="{{ url_for('booking.details', id=booking.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('booking.index') }}" class="btn btn-primary">
                        <i class="fas fa-list me-1"></i>عرض جميع الحجوزات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
